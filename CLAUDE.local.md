# Kotlin 命名规范

### 1. 包 (Packages)
**规则**: 全部小写，使用 `.` 作为分隔符，禁止使用下划线 `_`。
```kotlin
// ✅
package com.project.feature.data

// ❌
package com.project.Feature.data_source
```

### 2. 文件 (Files)
**规则**: PascalCase，与文件内核心声明的名称一致。
```kotlin
// ✅ 如果文件内核心是 class UserProfile
UserProfile.kt
```

### 3. 类型声明 (Classes, Interfaces, Enums)
**规则**: 使用 PascalCase。
```kotlin
// ✅
class UserProfile
interface NetworkClient
enum class Status
annotation class Inject
object AppConstants
```

### 4. 函数 (Functions)
**规则**: 使用 camelCase，以小写字母开头。
```kotlin
// ✅
fun calculateScore()
fun fetchUserData()

// ❌
fun CalculateScore()
fun fetch_user_data()
```

### 5. 测试函数 (Test Functions)
**规则**: 使用反引号 `` ` `` 包裹自然语言描述的测试场景。
```kotlin
// ✅
@Test
fun `given invalid credentials, login should fail`() { ... }
```

### 6. 属性与变量 (Properties & Variables)
**规则**: 使用 camelCase，以小写字母开头。
- **Backing Property**: 使用下划线 `_` 前缀。
- **Boolean**: 使用 `is`, `has`, `can`, `should` 等作为前缀。
```kotlin
// ✅
val userName: String
val isLoading: Boolean
private val _items = mutableListOf<String>()
val items: List<String> = _items
```

### 7. 常量 (Constants)
**规则**: `const val` 或 `object` 内的 `val` 使用 `SCREAMING_SNAKE_CASE`。
```kotlin
// ✅
const val MAX_RETRIES = 3
object Config {
    const val API_URL = "..."
}
```

### 8. 架构组件 (Architectural Components)
- **UseCase**: `动词 + 名词/名词短语 + UseCase`。
    - ✅ `class GetUserProfileUseCase`
- **Repository**: 接口为 `名词 + Repository`，实现类为 `...Impl`。
    - ✅ `interface UserRepository`
    - ✅ `class UserRepositoryImpl`
- **ViewModel**: `功能模块名 + ViewModel`。
    - ✅ `class UserProfileViewModel`

### 9. Jetpack Compose
- **Composable 函数**: 使用 PascalCase。
    - ✅ `@Composable fun UserProfileCard() { ... }`
- **Preview 函数**:
    1. 必须为 `private`。
    2. 命名为 `被预览组件名 + Preview`。
    3. 必须使用项目级统一的自定义预览注解 (`@GymBroPreview`)。
    ```kotlin
    // ✅
    @GymBroPreview
    @Composable
    private fun UserProfileCardPreview() {
        GymBroTheme {
            UserProfileCard()
        }
    }
    ```
## 🎯 核心优化目标

### 1. **严格禁止代码捏造**
- 所有代码必须基于code-examples和现有文件模式
- 禁止虚构任何API、接口或方法
- 必须参考指定的reference_example

### 2. **100%功能完整性**
- 先完成所有功能实装，再处理错误处理
- 禁止任何TODO、FIXME或占位符
- 确保编译通过和基础测试

### 3. **严格尺寸约束**
- 函数限制：≤80行
- 文件限制：≤500行
- 包结构：必须使用子包，禁止根目录文件

## 📋 Android MVI Architect 优化

### 强化职责
```yaml
原职责: 产出实现蓝图与代码
新职责: 产出100%完整实现蓝图，基于code-examples和现有模式
强制约束: 严格禁止代码捏造，必须基于现有code-examples和同类文件
```

### 代码参考优先级
1. **必须优先**: code-examples/README.md中对应示例
    - mvi_basic_implementation.kt (MVI结构)
    - repository_implementation.kt (数据层)
    - usecase_patterns.kt (业务逻辑)
    - contract_standards.kt (契约定义)
2. **次要参考**: 同模块现有文件的实现模式
3. **严格禁止**: 任何形式的代码捏造或虚构API
